defmodule Sentry.ConfigTracesSamplerTest do
  use ExUnit.Case, async: true

  describe "traces_sampler configuration validation" do
    defmodule TestSampler do
      def sample(_context), do: 0.5
    end

    test "accepts nil" do
      assert {:ok, nil} = Sentry.Config.__validate_traces_sampler__(nil)
    end

    test "accepts function with arity 1" do
      fun = fn _context -> 0.5 end
      assert {:ok, ^fun} = Sentry.Config.__validate_traces_sampler__(fun)
    end

    test "accepts MFA tuple with exported function" do
      assert {:ok, {TestSampler, :sample}} =
               Sentry.Config.__validate_traces_sampler__({TestSampler, :sample})
    end

    test "rejects MFA tuple with non-exported function" do
      assert {:error, error_msg} =
               Sentry.Config.__validate_traces_sampler__({TestSampler, :non_existent})

      assert error_msg =~ "function"
      assert error_msg =~ "is not exported"
    end

    test "rejects function with wrong arity" do
      fun = fn -> 0.5 end
      assert {:error, _} = Sentry.Config.__validate_traces_sampler__(fun)
    end

    test "rejects invalid types" do
      assert {:error, _} = Sentry.Config.__validate_traces_sampler__("invalid")
      assert {:error, _} = Sentry.Config.__validate_traces_sampler__(123)
      assert {:error, _} = Sentry.Config.__validate_traces_sampler__([])
    end
  end

  describe "tracing? function" do
    setup do
      original_rate = Sentry.Config.traces_sample_rate()
      original_sampler = Sentry.Config.traces_sampler()

      on_exit(fn ->
        Sentry.Config.put_config(:traces_sample_rate, original_rate)
        Sentry.Config.put_config(:traces_sampler, original_sampler)
      end)

      :ok
    end

    test "returns true when traces_sample_rate is set" do
      Sentry.Config.put_config(:traces_sample_rate, 0.5)
      Sentry.Config.put_config(:traces_sampler, nil)

      assert Sentry.Config.tracing?()
    end

    test "returns true when traces_sampler is set" do
      Sentry.Config.put_config(:traces_sample_rate, nil)
      Sentry.Config.put_config(:traces_sampler, fn _ -> 0.5 end)

      assert Sentry.Config.tracing?()
    end

    test "returns true when both are set" do
      Sentry.Config.put_config(:traces_sample_rate, 0.5)
      Sentry.Config.put_config(:traces_sampler, fn _ -> 0.5 end)

      assert Sentry.Config.tracing?()
    end

    test "returns false when neither is set" do
      Sentry.Config.put_config(:traces_sample_rate, nil)
      Sentry.Config.put_config(:traces_sampler, nil)

      refute Sentry.Config.tracing?()
    end
  end
end
